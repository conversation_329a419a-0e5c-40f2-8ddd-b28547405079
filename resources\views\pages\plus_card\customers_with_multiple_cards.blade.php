@extends('pages.build')
@section('title', 'Birden Fazla PlusCard Sahibi Müşteriler')
@php
    if (!function_exists('pastelColor')) {
        function pastelColor($seed) {
            srand(crc32($seed));
            $h = rand(0, 360);
            $s = 60 + rand(0, 20); // 60-80
            $l = 85 + rand(0, 10); // 85-95
            return "hsl($h, $s%, $l%)";
        }
    }
@endphp
@push('css')
<style>
        table.table tbody tr td {
        height: 10px !important;
    }

    table.table tbody td {
        height: 10px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        line-height: 10px !important;
        font-size: 10px !important;
        vertical-align: middle !important;
    }



    .form-check-label,
    .form-check-input {
        transform: scale(0.8);
        margin: 0;
    }

    .primary-radio {
        width: 10px !important;
        height: 10px !important;
        transform: scale(1) !important;
        margin-right: 4px !important;

        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border-radius: 50%;
        background-color: #9dada1ff; /* yeşil */
        border: 1px solid #ccc;
        cursor: pointer;
        position: relative;
    }

    .primary-radio:checked::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 6px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
    }


    textarea.form-control {
        font-size: 10px;
    }

    .btn {
        padding: 0.15rem 0.3rem;
        font-size: 10px;
    }
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-body">
                <h4>Birden Fazla PlusCard Sahibi Müşteriler</h4>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kart No</th>
                                <th>Müşteri ID</th>
                                <th>Müşteri Unvanı</th>
                                <th>Telefon Numarası</th>
                                <th>İlk Yükleyen Bayi</th>
                                <th>Son İşlem Yapan Bayi</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                        @forelse($items as $index => $item)
                            @php
                                $cardNumbers = explode(',', $item->kart_numaralari);
                                $branches = explode(',', $item->bayi_ismi);
                                $primaryStatuses = explode(',', $item->primary_status ?? '');
                                $customerName = $item->musteri_unvani ?? ($item->ad . ' ' . $item->soyad);
                                $groupName = "primarySelect_" . $item->customer_id;
                            @endphp

                            <tr>
                                <td colspan="8" style="font-weight:bold; color:#333; height:30px; line-height:30px; padding:0;">
                                    Müşteri: {{ $customerName }} (ID: {{ $item->customer_id }})
                                </td>
                            </tr>

                            @foreach($cardNumbers as $i => $kartNo)
                                @php
                                    $isPrimary = isset($primaryStatuses[$i]) && $primaryStatuses[$i] == '1';
                                    $cleanKartNo = str_replace([' ', '-', '(', ')', '.'], '', trim($kartNo));
                                    $ilkYukleyenBayileri = explode(',', $item->ilk_yukleyen_bayi ?? '');
                                    $sonIslemBayileri = explode(',', $item->son_islem_bayi ?? '');
                                @endphp
                                <tr style="">
                                    <td>{{ $i + 1 }}</td>
                                    <td>{{ $kartNo }}</td>
                                    <td>{{ $item->customer_id }}</td>
                                    <td>
                                        <div class="d-flex justify-content-center align-items-center gap-2">
                                            <span>{{ $customerName }}</span>
                                            <div>
                                                <input class="form-check-input primary-radio"
                                                       type="radio"
                                                       name="{{ $groupName }}"
                                                       data-customer-id="{{ $item->customer_id }}"
                                                       data-card-no="{{ trim($kartNo) }}"
                                                       {{ $isPrimary ? 'checked' : '' }}>
                                                <label class="form-check-label ms-1">Birincil Kayıt Seç</label>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $item->telefon ?? '-' }}</td>
                                    <td>{{ trim($ilkYukleyenBayileri[$i] ?? '-') }}</td>
                                    <td>{{ trim($sonIslemBayileri[$i] ?? '-') }}</td>
                                    <td>
                                        <button class="btn btn-primary note-btn"
                                                data-bs-toggle="modal"
                                                data-bs-target="#noteModal{{ $item->customer_id }}_{{ $cleanKartNo }}"
                                                data-customer-id="{{ $item->customer_id }}"
                                                data-card-no="{{ trim($kartNo) }}">
                                            Not Ekle
                                        </button>

                                        <div class="modal fade"
                                             id="noteModal{{ $item->customer_id }}_{{ $cleanKartNo }}"
                                             tabindex="-1"
                                             aria-labelledby="noteModalLabel{{ $item->customer_id }}_{{ $cleanKartNo }}"
                                             aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="noteModalLabel{{ $item->customer_id }}_{{ $cleanKartNo }}">
                                                            Not Ekle (Müşteri ID: {{ $item->customer_id }}, Kart No: {{ $kartNo }})
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <textarea id="noteText{{ $item->customer_id }}_{{ $cleanKartNo }}" class="form-control" rows="4" placeholder="Notunuzu giriniz..."></textarea>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                                                        <button type="button"
                                                                class="btn btn-primary save-note-btn"
                                                                data-customer-id="{{ $item->customer_id }}"
                                                                data-card-no="{{ trim($kartNo) }}"
                                                                data-clean-card-no="{{ $cleanKartNo }}">
                                                            Kaydet
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @empty
                            <tr><td colspan="8">Kayıt bulunamadı.</td></tr>
                        @endforelse
                        </tbody>
                    </table>

                    <div class="mt-3 d-flex justify-content-center">
                        {{ $items->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.note-btn').forEach(function (btn) {
        btn.addEventListener('click', function () {
            var customerId = this.getAttribute('data-customer-id');
            var cardNo = this.getAttribute('data-card-no');

            var cleanCardNo = cardNo.replace(/[\s\-\(\)\.]/g, '');
            axios.post('/plus-card/get-customer-note', {
                customer_id: customerId,
                card_no: cardNo,
                _token: '{{ csrf_token() }}'
            }).then(function (response) {
                if (response.data.success && response.data.note) {
                    document.getElementById('noteText' + customerId + '_' + cleanCardNo).value = response.data.note;
                }
            }).catch(function () {
            });
        });
    });

    document.querySelectorAll('.primary-radio').forEach(function (radio) {
        radio.addEventListener('change', function () {
            var cardNo = this.getAttribute('data-card-no');
            var customerId = this.getAttribute('data-customer-id');

            axios.post('{{ route('plus-card.set-primary') }}', {
                card_no: cardNo,
                customer_id: customerId,
                type: 'multiple_cards',
                _token: '{{ csrf_token() }}'
            }).then(function (response) {
                if (response.data.success) {
                    document.querySelectorAll('input[name="primarySelect_' + customerId + '"]').forEach(function (el) {
                        el.checked = false;
                    });
                    document.querySelector('input[name="primarySelect_' + customerId + '"][data-card-no="' + cardNo + '"]').checked = true;
                    alert('Birincil kayıt olarak ayarlandı!');
                } else {
                    alert('Seçim hatası: ' + (response.data.message || 'Bilinmeyen hata'));
                }
            }).catch(function () {
                alert('İşlem sırasında hata oluştu.');
            });
        });
    });

    document.querySelectorAll('.save-note-btn').forEach(function (btn) {
        btn.addEventListener('click', function () {
            var customerId = this.getAttribute('data-customer-id');
            var cardNo = this.getAttribute('data-card-no');
            var cleanCardNo = this.getAttribute('data-clean-card-no');
            var note = document.getElementById('noteText' + customerId + '_' + cleanCardNo).value;

            axios.post('/plus-card/customer-note', {
                key: 'customer_' + customerId + '_pluscard',
                note: note,
                customer_id: customerId,
                card_no: cardNo,
                _token: '{{ csrf_token() }}'
            }).then(function (response) {
                if (response.data.success) {
                    alert('Not kaydedildi!');
                    var modal = bootstrap.Modal.getInstance(document.getElementById('noteModal' + customerId + '_' + cleanCardNo));
                    if (modal) modal.hide();
                } else {
                    alert('Kayıt başarısız: ' + (response.data.message || 'Bilinmeyen hata'));
                }
            }).catch(function () {
                alert('Kayıt sırasında hata oluştu!');
            });
        });
    });
});
</script>
@endsection
