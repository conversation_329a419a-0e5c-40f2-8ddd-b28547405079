<?php

namespace App\Exports;

use App\Models\Branch;
use App\Models\Car;
use App\Models\Customer;
use App\Models\Expertise;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\Stock;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExpertisesExport extends ExcelHeaderLogoAdder implements
    FromQuery,
    WithHeadings,
    WithStyles,
    WithMapping,
    WithChunkReading
{
    protected $type;
    protected $search;
    protected $start_date;
    protected $end_date;
    protected $belge_no;
    protected $branch_id;
    protected $uuid;
    protected $payment_type;
    protected $customerId;
    protected $expertise_campaing;

    public function __construct(private array $filters = [])
    {
        $this->type = $filters['type'];
        $this->search = $filters['search'];
        $this->start_date = Carbon::make($filters['start_date'])->format('Y-m-d');
        $this->end_date = Carbon::make($filters['end_date'])->format('Y-m-d');
        $this->belge_no = $filters['belge_no'];
        $this->branch_id = $filters['branch_id'];
        $this->uuid = $filters['uuid'];
        $this->payment_type = $filters['payment_type'];
        $this->customerId = $filters['customerId'];
        $this->expertise_campaing = $filters['expertise_campaing'];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function headings(): array
    {
        return [
            'ID',
            'KAYIT YERİ',
            'Şube Kodu',
            'Şube',
            'Şube Kısa Ad',
            'Srv. Şubesi',
            'Srv. Şubesi Kısa Ad',
            'Srv. Başka Şube',
            'Belge Özel Kod',
            'Belge Tarihi',
            'Çıkış Tarihi',
            'Belge No',
            'Bize Nerden Ulaştınız',
            'Müşteri Unvanı',
            'Satıcı Unvanı',
            'Plaka',
            'Şase No',
            'Araç KM',
            'Model Yılı',
            'Ağırlık',
            'Çekişi',
            'Yayın Yasağı',
            'Sigorta T.',
            'Hizmet Cinsi',
            'Satış Belge Tipi',
            'Geçerli Kampanya',
            'Yol Yardımı',
            'Hasar Sorgula',
            'İnd. Kuponu',
            'Api Onay Kodu',
            'Api Mesajı',
            'Liste Fiyatı',
            'Hizmet Tutarı',
            'İndirim Tutarı',
            'Tahsilat Tipi',
            'Sözleşme No',
            'Sözleşme Kodu',
            'Kupon',
            'Nakit Tutar',
            'Kredi Tutar',
            'Durumu',
            'İşlem OK',
            'Ftp OK',
            'Hasar S.',
            'Kilometre S.',
            'Borç S.',
            'Ruhsat S.',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1    => [
                'font' => [
                    'bold' => true,
                    'color'=>[
                        'rgb'=>Color::COLOR_WHITE
                    ]
                ],
                'fill' => [
                    'fillType'   => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => Color::COLOR_RED
                    ],
                ],
            ],
        ];
    }

    public function map($item): array
    {
        if (property_exists($item, 'T400_ID')) {
            return $this->oldMap($item);
        }

        return $this->newMap($item);
    }

    private function newMap($item): array
    {
        // Raw SQL'den gelen verileri kullan
        $hizmet = $item->hizmet_cesitleri ?? '';
        $kampanya = $item->kampanyalar ?? '';
        $yolYardimi = $item->yol_yardimi ?? 0;
        $hasarSorgula = $item->sorgu_hizmeti ?? 0;
        $hizmetTutari = $item->toplam_tutar ?? 0;
        $iskontoTutari = $item->iskonto_amount ?? 0;
        $tahsilat = $item->tahsilat_tipleri ?? '';
        $contractNo = $item->contract_no ?? '';
        $contractCode = $item->contract_code ?? '';
        $kupon = $item->kupon_tutar ?? 0;
        $nakit = $item->nakit_tutar ?? 0;
        $kredi = $item->kredi_tutar ?? 0;
        $listeFiyati = $item->liste_fiyati ?? 0;

        // UMR-DEV-342, we replace sozlesme with Kurumsal Satış.
        $tahsilat = str_replace('sozlesme', 'Kurumsal Satış', $tahsilat);

        // Customer full names
        $aliciFullName = trim(($item->alici_ad ?? '') . ' ' . ($item->alici_soyad ?? ''));
        $cariFullName = trim(($item->cari_ad ?? '') . ' ' . ($item->cari_soyad ?? ''));

        return [
            $item->id, //     'ID',
            'Masa Üstü', //        'KAYIT YERİ',
            $item->branch_kod ?? '', //   'Şube Kodu',
            $item->register_branch_unvan ?? '', //   'Şube',
            $item->register_branch_kisa_ad ?? '', //     'Şube Kısa Ad',
            $item->branch_unvan ?? '', //        'Srv. Şubesi',
            $item->branch_kisa_ad ?? '', //   'Srv. Şubesi Kısa Ad',
            $item->branch_unvan ?? '', //      'Srv. Başka Şube',
            $item->belge_ozel_kodu == 1 ? 'Onaylı' : 'Onaysız', //       'Belge Özel Kod',
            $item->belge_tarihi ? Carbon::make($item->belge_tarihi)->format('d.m.Y H:i:s') : '', //      'Belge Tarihi',
            $item->cikis_tarihi ? Carbon::make($item->cikis_tarihi)->format('d.m.Y H:i:s') : '', //          'Çıkış Tarihi',
            $item->belge_no, //    'Belge No',
            $item->nereden_ulastiniz, //    'Bize Nerden Ulaştınız',
            $aliciFullName, //    'Müşteri Unvanı',
            $cariFullName, //      'Satıcı Unvanı',
            $item->plaka ?? '', //   'Plaka',
            $item->sase_no ?? '', // 'Şase No',
            $item->km ?? '', //     'Araç KM',
            $item->model_yili ?? '', //       'Model Yılı',
            $item->net_agirlik ?? '', //   'Ağırlık',
            $item->cekis ?? '', //    'Çekişi',
            $item->yayin_yasagi == 1 ? 'Evet' : 'Hayır', //   'Yayın Yasağı',
            $item->sigorta_teklif_ver ? 'Evet' : 'Hayır', //   'Sigorta T.',
            rtrim($hizmet, ','), //         'Hizmet Cinsi',
            'Satış Fatura', //      'Satış Belge Tipi',
            rtrim($kampanya, ','), //  'Geçerli Kampanya',
            $yolYardimi ? 'VAR' : 'YOK', //  'Yol Yardımı',
            $hasarSorgula ? 'EVET' : 'HAYIR', //  'Hasar Sorgula',
            '', //    'İnd. Kuponu',
            '', //         'Api Onay Kodu',
            '', //    'Api Mesajı',
            $listeFiyati ?: 0, //       'Liste Fiyatı',
            $hizmetTutari ?: 0, //    'Hizmet Tutarı',
            $iskontoTutari ?: 0, // 'İndirim Tutarı',
            rtrim($tahsilat, ',') ?: '', //      'Tahsilat Tipi',
            $contractNo ?: '', // Sozlesme No
            $contractCode ?: '', // Sozlesme Kodu
            $kupon ?: '', //     'Kupon',
            $nakit ?: '', //   'Nakit Tutar',
            $kredi ?: '', //       'Kredi Tutar',
            '', //        'Durumu',
            '', //   'İşlem OK',
            '', //          'Ftp OK',
            $item->hasar_sorgu_sonuc, //   'Hasar S.',
            $item->kilometre_sorgu_sonuc, //     'Kilometre S.',
            $item->borc_sorgu_sonuc, //       'Borç S.',
            $item->ruhsat_sorgu_sonuc, //          'Ruhsat S.',
        ];
    }

    private function oldMap($item)
    {
        $branch = Branch::where('belge_kod', $item->T400_subekodu)->first();
        $alici = Customer::where('kod', $item->T400_musteri_UQ)->first();
        $satici = Customer::where('kod', $item->T400_satici_UQ)->first();
        $car = Car::where('UQ', $item->T400_arac_UQ)->first();
        $stock = Stock::where('kod', $item->T400_hizmet_UQ)->first();

        $payment_type = 'Diğer';

        if (!empty($item->T400_KUPON_UQ)) {
            $payment_type = "Plus Card";
        }

        $nakit_tutar = "0";
        $plus_kart_tutar = '';

        if ($payment_type == "plus_kart") {
            $plus_kart_tutar = number_format($item->T400_tlsatistutari, 2, ',', '.') . "₺";
        }

        if (is_null($item->T400_kredi_tutar)) {
            // Eğer T400_kredi_tutar boşsa, T400_tlsatistutari'yi yazdır
            $nakit_tutar = $item->T400_tlsatistutari;
        } else {
            // Eğer T400_kredi_tutar boş değilse, T400_tlsatistutari'den çıkar ve pozitif olarak yazdır
            $result_nakit = $item->T400_tlsatistutari - $item->T400_kredi_tutar;
            // Sonucu pozitif hale getir
            $result_nakit = abs($result_nakit);
            $nakit_tutar = $result_nakit;
        }

        return [
            $item->T400_ID, //     'ID',
            'Masa Üstü', //        'KAYIT YERİ',
            optional($branch)->kod, //   'Şube Kodu',
            optional($branch)->unvan, //   'Şube',
            optional($branch)->kisa_ad, //     'Şube Kısa Ad',
            optional($branch)->unvan, //        'Srv. Şubesi',
            optional($branch)->kisa_ad, //   'Srv. Şubesi Kısa Ad',
            optional($branch)->unvan, //      'Srv. Başka Şube',
            $item->T400_belgeozelkodu == 1 ? 'Onaylı' : 'Onaysız', //       'Belge Özel Kod',
            $item->T400_belgetarihi ? Carbon::make($item->T400_belgetarihi)->format('d.m.Y H:i:s') : '', //      'Belge Tarihi',
            $item->T400_cikistarihi ? Carbon::make($item->T400_cikistarihi)->format('d.m.Y H:i:s') : '', //          'Çıkış Tarihi',
            $item->T400_belgeno, //    'Belge No',
            $item->T400_reklamulasimyeri, //    'Bize Nerden Ulaştınız',
            optional($alici)->name . ' ' . optional($alici)->surname, //    'Müşteri Unvanı',
            optional($satici)->name . ' ' . optional($satici)->surname, //      'Satıcı Unvanı',
            optional($car)->plaka, //   'Plaka',
            optional($car)->sase_no, // 'Şase No',
            optional($car)->km, //     'Araç KM',
            optional($car)->model_yili, //       'Model Yılı',
            optional($car)->net_agirlik, //   'Ağırlık',
            optional($car)->cekis, //    'Çekişi',
            $item->T400_sitedeyayinyasak == 1 ? 'Evet' : 'Hayır', //   'Yayın Yasağı',
            $item->T400_sigorta_teklifi ? 'Evet' : 'Hayır', //   'Sigorta T.',
            $stock?->ad, //         'Hizmet Cinsi',
            'Satış Fatura', //      'Satış Belge Tipi',
            '', //  'Geçerli Kampanya',
            $item->T400_yol_yardim ? 'VAR' : 'YOK', //  'Yol Yardımı',
            $item->T400_hasar_sorgulama ? 'EVET' : 'HAYIR', //  'Hasar Sorgula',
            $item->T400_indirimkuponno, //    'İnd. Kuponu',
            $item->T400_api_onay_kodu, //         'Api Onay Kodu',
            $item->T400_api_sonuc_mesaji, //    'Api Mesajı',
            number_format($item->T400_tllistefiyati, 2, ',', '.') . "₺" ?? '0', //       'Liste Fiyatı',
            number_format($item->T400_tlsatistutari, 2, ',', '.') . "₺", //    'Hizmet Tutarı',
            number_format(($item->T400_tllistefiyati - $item->T400_tlsatistutari), 2, ',', '.') . "₺", // 'İndirim Tutarı',
            $payment_type, //      'Tahsilat Tipi',
            '', // Sozlesme No
            '', // Sozlesme Kodu
            $plus_kart_tutar ?: '', //     'Kupon',
            $nakit_tutar, //   'Nakit Tutar',
            $item->T400_kredi_tutar ?: '', //       'Kredi Tutar',
            '', //        'Durumu',
            '', //   'İşlem OK',
            $item->T400_ftpgonderildi ? 'Evet' : 'Hayır', //          'Ftp OK',
            $item->T400_hasar_sorgulama, //   'Hasar S.',
            '', //     'Kilometre S.',
            '', //       'Borç S.',
            '', //          'Ruhsat S.',
        ];
    }

    /**
     * Process 1000 records at a time
     *
     * @return int
     */
    public function chunkSize(): int
    {
        return 1000;
    }

    public function query()
    {
        $oldSystemDate = Carbon::make('2024-01-01')->format('Y-m-d');

        // New System
        // 2023-12-17 18:12:23 start
        // Old System
        // 2014-11-15 08:20:07.000 start
        // 2024-03-25 16:12:17.000 end

        if ($this->start_date <= $oldSystemDate) {
            return $this->oldQuery();
        }

        return $this->newQuery();
    }

    private function newQuery()
    {
        $uuid = $this->uuid;
        $payment_type = $this->payment_type;
        $expertise_campaing = $this->expertise_campaing;

        // Raw SQL query for better performance - using subqueries to avoid GROUP BY issues
        $query = DB::table('expertises as e')
            ->select([
                'e.id',
                'e.belge_ozel_kodu',
                'e.belge_tarihi',
                'e.cikis_tarihi',
                'e.belge_no',
                'e.nereden_ulastiniz',
                'e.yayin_yasagi',
                'e.sigorta_teklif_ver',
                'e.hasar_sorgu_sonuc',
                'e.kilometre_sorgu_sonuc',
                'e.borc_sorgu_sonuc',
                'e.ruhsat_sorgu_sonuc',
                'e.branch_id',
                'e.kayit_branch_id',
                'e.cari_id',
                'e.satici_id',
                'e.alici_id',
                'e.car_id',
                // Branch bilgileri
                'b.kod as branch_kod',
                'b.unvan as branch_unvan',
                'b.kisa_ad as branch_kisa_ad',
                'rb.unvan as register_branch_unvan',
                'rb.kisa_ad as register_branch_kisa_ad',
                // Customer bilgileri
                'cari.unvan as cari_unvan',
                'cari.ad as cari_ad',
                'cari.soyad as cari_soyad',
                'satici.unvan as satici_unvan',
                'satici.ad as satici_ad',
                'satici.soyad as satici_soyad',
                'alici.unvan as alici_unvan',
                'alici.ad as alici_ad',
                'alici.soyad as alici_soyad',
                // Car bilgileri
                'car.plaka',
                'car.sase_no',
                'car.km',
                'car.model_yili',
                'car.net_agirlik',
                'car.cekis',
                // Stock bilgileri - using subquery to get first record
                DB::raw('(SELECT liste_fiyati FROM expertise_stocks WHERE expertise_id = e.id LIMIT 1) as liste_fiyati'),
                DB::raw('(SELECT iskonto_amount FROM expertise_stocks WHERE expertise_id = e.id LIMIT 1) as iskonto_amount'),
                DB::raw('(SELECT yol_yardimi FROM expertise_stocks WHERE expertise_id = e.id LIMIT 1) as yol_yardimi'),
                DB::raw('(SELECT sorgu_hizmeti FROM expertise_stocks WHERE expertise_id = e.id LIMIT 1) as sorgu_hizmeti'),
                // Payment aggregations using subqueries
                DB::raw('(SELECT SUM(amount) FROM expertise_payments WHERE expertise_id = e.id AND type = "plus_kart" AND deleted_at IS NULL) as plus_kart_tutar'),
                DB::raw('(SELECT SUM(amount) FROM expertise_payments WHERE expertise_id = e.id AND type IN ("nakit", "banka") AND deleted_at IS NULL) as nakit_tutar'),
                DB::raw('(SELECT SUM(amount) FROM expertise_payments WHERE expertise_id = e.id AND type = "kredi_karti" AND deleted_at IS NULL) as kredi_tutar'),
                DB::raw('(SELECT SUM(amount) FROM expertise_payments WHERE expertise_id = e.id AND deleted_at IS NULL) as toplam_tutar'),
                DB::raw('(SELECT GROUP_CONCAT(DISTINCT type) FROM expertise_payments WHERE expertise_id = e.id AND deleted_at IS NULL) as tahsilat_tipleri'),
                DB::raw('(SELECT MAX(payment_code) FROM expertise_payments WHERE expertise_id = e.id AND type IN ("nakit", "banka", "kredi_karti", "sozlesme") AND deleted_at IS NULL) as contract_no'),
                DB::raw('(SELECT MAX(payment_detail) FROM expertise_payments WHERE expertise_id = e.id AND type IN ("nakit", "banka", "kredi_karti", "sozlesme") AND deleted_at IS NULL) as contract_code'),
                DB::raw('(SELECT SUM(pca.unit_price) FROM expertise_payments ep LEFT JOIN plus_card_credi_and_puan_add pca ON ep.plus_card_odeme_id = pca.id WHERE ep.expertise_id = e.id AND ep.type = "plus_kart" AND ep.deleted_at IS NULL AND pca.unit_price IS NOT NULL) as kupon_tutar'),
                DB::raw('(SELECT GROUP_CONCAT(DISTINCT s.ad) FROM expertise_stocks es LEFT JOIN stocks s ON es.stock_id = s.id WHERE es.expertise_id = e.id) as hizmet_cesitleri'),
                DB::raw('(SELECT GROUP_CONCAT(DISTINCT CASE WHEN c.name IS NOT NULL THEN c.name ELSE "-" END) FROM expertise_stocks es LEFT JOIN campaigns c ON es.campaign_id = c.id WHERE es.expertise_id = e.id) as kampanyalar')
            ])
            ->leftJoin('branches as b', 'e.branch_id', '=', 'b.id')
            ->leftJoin('branches as rb', 'e.kayit_branch_id', '=', 'rb.id')
            ->leftJoin('customers as cari', 'e.cari_id', '=', 'cari.id')
            ->leftJoin('customers as satici', 'e.satici_id', '=', 'satici.id')
            ->leftJoin('customers as alici', 'e.alici_id', '=', 'alici.id')
            ->leftJoin('cars as car', 'e.car_id', '=', 'car.id')
            ->where('e.status', '!=', -1)
            ->where('e.manuel_save', 1)
            ->where('e.car_id', '>', 0)
            ->where('e.cari_id', '>', 0)
            ->where('e.satici_id', '>', 0)
            ->where('e.alici_id', '>', 0);

        // Payment type filter
        if (isset($payment_type) && $payment_type != 'all') {
            $query->whereExists(function($subquery) use ($payment_type) {
                $subquery->select(DB::raw(1))
                         ->from('expertise_payments')
                         ->whereRaw('expertise_payments.expertise_id = e.id')
                         ->where('expertise_payments.type', $payment_type)
                         ->whereNull('expertise_payments.deleted_at');
            });
        }

        // Campaign filter
        if (isset($expertise_campaing) && $expertise_campaing != 'all') {
            $query->whereExists(function($subquery) use ($expertise_campaing) {
                $subquery->select(DB::raw(1))
                         ->from('expertise_stocks')
                         ->whereRaw('expertise_stocks.expertise_id = e.id')
                         ->where('expertise_stocks.campaign_id', $expertise_campaing);
            });
        }

        // Branch filter
        if (is_array($this->branch_id)) {
            $query->whereIn('e.branch_id', $this->branch_id);
        } elseif (!empty($this->branch_id) && $this->branch_id != 'all') {
            $query->where('e.branch_id', $this->branch_id);
        }

        // Customer filter
        if (!empty($this->customerId) && $this->customerId != '') {
            $query->where(function ($q) {
                return $q->where('e.cari_id', $this->customerId)
                    ->orWhere('e.satici_id', $this->customerId)
                    ->orWhere('e.alici_id', $this->customerId);
            });
        }

        // Date filter
        if (empty($this->search)) {
            $query->whereDate('e.belge_tarihi', '>=', $this->start_date)
                ->whereDate('e.belge_tarihi', '<=', $this->end_date);
        }

        // Search filters
        if ($this->search) {
            if ($this->type == 'customer' && !empty($this->search)) {
                $query->where(function ($q) {
                    $q->where('cari.unvan', 'like', "%" . $this->search . "%")
                        ->orWhere('satici.unvan', 'like', "%" . $this->search . "%")
                        ->orWhere('alici.unvan', 'like', "%" . $this->search . "%");
                });
            } else if ($this->type == 'plaka' && !empty($this->search)) {
                $search = str_replace(' ', '', $this->search);
                $search = convertTurkishToEnglish($search);
                $query->where('car.plaka', 'LIKE', "%" . $search . "%");
            } elseif ($this->type == "sase" && !empty($this->search)) {
                $query->where('car.sase_no', 'LIKE', "%" . $this->search . "%");
            }
        }

        // Belge no filter
        if (!empty($this->belge_no)) {
            $query->where('e.belge_no', 'LIKE', "%" . $this->belge_no . "%");
        }

        $query->orderBy('e.created_at', 'desc');

        return $query;
    }

    private function oldQuery()
    {
        $payment_type = $this->payment_type;

        $startDate = Carbon::make($this->start_date)->format('Y-m-d');
        $endDate = Carbon::make($this->end_date)->format('Y-m-d');

        return DB::connection('mysql2')
            ->table('T400_SRVBASLIK')
            ->when(isset($payment_type) && $payment_type != 'all', function ($query) use ($payment_type) {
                if ($payment_type == 'plus_kart') {
                    return $query->whereNotNull('T400_KUPON_UQ');
                } else {
                    return $query->whereNull('T400_KUPON_UQ');
                }
            })
            ->when($this->branch_id != 'all', function ($query) {
                return $query->where(function ($query) {
                    if (is_array($this->branch_id)) {
                        $branches = Branch::whereIn('id', $this->branch_id)->pluck('belge_kod')->toArray();
                        return $query->whereIn('T400_subekodu', $branches);
                    } elseif (!empty($this->branch_id) && $this->branch_id != 'all') {
                        $branches = Branch::where('id', $this->branch_id)->pluck('belge_kod')->toArray();
                        return $query->whereIn('T400_subekodu', $branches);
                    }
                });
            })
            ->when(!empty($this->customerId) && $this->customerId != '', function ($query) {
                $customer = Customer::where('id', $this->customerId)->first();
                $query->where(function ($query) use ($customer) {
                    return $query->where('T400_musteri_UQ', $customer->kod)
                        ->orWhere('T400_satici_UQ', $customer->kod);
                });
            })
            ->when(empty($this->search), function ($query) use ($startDate, $endDate) {
                $query->whereDate('T400_belgetarihi', '>=', $startDate)
                    ->whereDate('T400_belgetarihi', '<=', $endDate);
            })
            ->when($this->search, function ($query) {
                if ($this->type == 'customer' && !empty($this->search)) {
                    $customer = Customer::where('unvan', 'like', "%" . $this->search . "%")->pluck('kod')->toArray();

                    return $query->whereIn('T400_satici_UQ', $customer)->orWhereIn('T400_musteri_UQ', $customer);
                } else if ($this->type == 'plaka' && !empty($this->search)) {
                    $searchWithoutSpaces = str_replace(' ', '', $this->search);
                    $search = str_replace(' ', '', $this->search);
                    $search = convertTurkishToEnglish($search);
                    $car = Car::where('plaka', 'LIKE', "%" . $search . "%")->pluck('UQ')->toArray();
                    return $query->whereIn('T400_arac_UQ', $car);
                } elseif ($this->type == "sase" && !empty($this->search)) {
                    $car = Car::where('sase_no', 'LIKE', "%" . $this->search . "%")->pluck('UQ')->toArray();
                    return $query->whereIn('T400_arac_UQ', $car);
                }
            })
            ->when(!empty($this->belge_no), function ($query) {
                return $query->where('T400_belgeno', 'LIKE', "%" . $this->belge_no . "%");
            })
            ->orderBy('T400_belgetarihi', 'desc');
    }
}
