<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class PlusCardService
{
    /**
     * Aynı no'ya sahip ve birden fazla farklı customer_id'ye sahip kartları getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getDuplicateNoCardsGrouped($perPage, $branchIds = null)
    {
        $duplicateNos = PlusCard::select('no')
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->groupBy('no')
            ->havingRaw('COUNT(DISTINCT customer_id) > 1')
            ->orderBy('no')
            ->get()
            ->pluck('no');

        $grouped = PlusCard::select([
                'plus_cards.*',
                'last_transaction_branch.kisa_ad as son_islem_bayi'
            ])
            ->whereIn('no', $duplicateNos)
            ->with(['getBranch', 'getCustomer'])
            ->leftJoin('plus_card_credi_and_puan_add as last_transaction', function($join) {
                $join->on('last_transaction.card_id', '=', 'plus_cards.id')
                     ->whereRaw('last_transaction.id = (SELECT MAX(id) FROM plus_card_credi_and_puan_add WHERE card_id = plus_cards.id AND deleted_at IS NULL)');
            })
            ->leftJoin('users as last_transaction_user', 'last_transaction_user.id', '=', 'last_transaction.user_id')
            ->leftJoin('branches as last_transaction_branch', 'last_transaction_branch.id', '=', 'last_transaction_user.branch_id')
            ->whereNull('plus_cards.deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('plus_cards.branch_id', $branchIds);
            })
            ->orderBy('plus_cards.no')
            ->orderBy('plus_cards.customer_id')
            ->get()
            ->groupBy('no');

        $currentPage = LengthAwarePaginator::resolveCurrentPage('page');
        $groupsPerPage = $grouped->forPage($currentPage, $perPage);

        $paginated = new LengthAwarePaginator(
            $groupsPerPage,
            $grouped->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return $paginated;
    }

    /**
     * Birden fazla pluscard'ı olan müşterileri getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCustomersWithMultipleCards($perPage = 10, $branchIds = null)
    {
        $baseQuery = PlusCard::query()
            ->select([
                'plus_cards.customer_id',
                'customers.ad',
                'customers.soyad',
                'customers.telefon',
                'customers.unvan as musteri_unvani',
                DB::raw('COUNT(plus_cards.customer_id) as kart_sayisi'),
                DB::raw("GROUP_CONCAT(plus_cards.id ORDER BY plus_cards.id SEPARATOR ',') as kart_idleri"),
                DB::raw("GROUP_CONCAT(plus_cards.no ORDER BY plus_cards.id SEPARATOR ', ') as kart_numaralari"),
                DB::raw("GROUP_CONCAT(branches.kisa_ad ORDER BY plus_cards.id SEPARATOR ', ') as bayi_ismi"),
                DB::raw("GROUP_CONCAT(plus_cards.is_primary ORDER BY plus_cards.id SEPARATOR ', ') as primary_status"),
                DB::raw("GROUP_CONCAT(branches.kisa_ad ORDER BY plus_cards.id SEPARATOR ', ') as ilk_yukleyen_bayi")
            ])
            ->join('customers', 'customers.id', '=', 'plus_cards.customer_id')
            ->leftJoin('branches', 'branches.id', '=', 'plus_cards.branch_id')
            ->whereNull('plus_cards.deleted_at');

        if ($branchIds !== null && !empty($branchIds)) {
            $baseQuery->whereIn('plus_cards.branch_id', $branchIds);
        }

        $results = $baseQuery->groupBy(
                'plus_cards.customer_id',
                'customers.ad',
                'customers.soyad',
                'customers.telefon',
                'customers.unvan'
            )
            ->havingRaw('COUNT(plus_cards.customer_id) > 1')
            ->paginate($perPage);

        foreach ($results as $item) {
            $kartIdleri = explode(',', $item->kart_idleri);
            $sonIslemBayileri = [];

            foreach ($kartIdleri as $kartId) {
                $sonIslem = DB::table('plus_card_credi_and_puan_add')
                    ->leftJoin('users', 'users.id', '=', 'plus_card_credi_and_puan_add.user_id')
                    ->leftJoin('branches', 'branches.id', '=', 'users.branch_id')
                    ->where('plus_card_credi_and_puan_add.card_id', $kartId)
                    ->whereNull('plus_card_credi_and_puan_add.deleted_at')
                    ->where('plus_card_credi_and_puan_add.user_id', '>', 0)
                    ->orderBy('plus_card_credi_and_puan_add.id', 'desc')
                    ->first(['branches.kisa_ad']);

                $sonIslemBayileri[] = $sonIslem ? $sonIslem->kisa_ad : '-';
            }

            $item->son_islem_bayi = implode(', ', $sonIslemBayileri);
        }

        return $results;
    }

    /**
     * Bir müşteri ve kart için plus_cards tablosunda not kaydeder
     */
    public function saveCustomerNote($customerId, $note, $key = null, $cardNo)
    {
        $plusCard = PlusCard::where('customer_id', $customerId)
            ->where('no', $cardNo)
            ->first();
        if ($plusCard) {
            $plusCard->note = $note;
            $plusCard->save();
            return true;
        }
        return false;
    }
}